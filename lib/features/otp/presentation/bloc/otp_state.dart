import 'package:freezed_annotation/freezed_annotation.dart';

part 'otp_state.freezed.dart';

@freezed
abstract class OtpState with _$OtpState {
  const factory OtpState({
    @Default('') String otpCode,
    @Default(300) int remainingSeconds,
    @Default(false) bool isLoading,
    @Default(false) bool canResend,
    String? errorMessage,
    @Default('') String email,
    @Default('') String referenceCode,
    @Default(OtpStatus.idle) OtpStatus status,
  }) = _OtpState;
}

enum OtpStatus { idle, loading, success, error, resendSuccess }
